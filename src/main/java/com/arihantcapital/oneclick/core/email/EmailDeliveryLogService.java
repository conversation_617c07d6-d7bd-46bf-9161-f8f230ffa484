package com.arihantcapital.oneclick.core.email;


import com.arihantcapital.oneclick.core.model.email.EmailDeliveryResult;
import com.arihantcapital.oneclick.domain.entity.log.email.EmailDeliveryLog;
import com.arihantcapital.oneclick.domain.entity.log.email.EmailStatus;
import com.arihantcapital.oneclick.domain.repository.EmailDeliveryLogRepository;
import jakarta.transaction.Transactional;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class EmailDeliveryLogService {
    private final EmailDeliveryLogRepository repository;

    public EmailDeliveryLogService(EmailDeliveryLogRepository repository) {
        this.repository = repository;
    }

    public void logSuccess(EmailDeliveryLog log, EmailDeliveryResult result) {
        log.setStatus(EmailStatus.SENT);
        log.setProvider(result.getProvider());
        log.setAttempts(result.getAttempts());
        log.setSentAt(result.getTimestamp());
        repository.save(log);
    }

    public void logFailure(EmailDeliveryLog log, EmailException exception) {
        log.setStatus(EmailStatus.FAILED);
        log.setErrorMessage(exception.getMessage());
        repository.save(log);
    }
}