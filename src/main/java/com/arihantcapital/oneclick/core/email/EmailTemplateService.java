package com.arihantcapital.oneclick.core.email;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service(value = "emailTemplateService")
public class EmailTemplateService {
    private final TemplateEngine templateEngine;
    private final Map<String, String> templateCache = new ConcurrentHashMap<>();

    public EmailTemplateService(TemplateEngine templateEngine) {
        this.templateEngine = templateEngine;
        loadTemplates();
    }

    @PostConstruct
    public void loadTemplates() {
        try {
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources("classpath:templates/email/*.html");

            for (Resource resource : resources) {
                String templateName = getTemplateNameFromResource(resource);
                String content = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
                templateCache.put(templateName, content);
                log.info("Loaded email template: {}", templateName);
            }
        } catch (IOException e) {
            log.error("Failed to load email templates", e);
        }
    }

    public String processTemplate(String templateName, Map<String, Object> variables) {
        if (templateEngine != null) {
            // Use Thymeleaf for advanced templating
            Context context = new Context();
            context.setVariables(variables);
            return templateEngine.process("email/" + templateName, context);
        } else {
            // Fallback to simple string replacement
            return processSimpleTemplate(templateName, variables);
        }
    }

    private String processSimpleTemplate(String templateName, Map<String, Object> variables) {
        String template = templateCache.get(templateName);
        if (template == null) {
            throw new EmailException("Template not found: " + templateName);
        }

        String result = template;
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            result = result.replace(placeholder, String.valueOf(entry.getValue()));
        }

        return result;
    }

    private String getTemplateNameFromResource(Resource resource) {
        String filename = resource.getFilename();
        return filename != null ? filename.substring(0, filename.lastIndexOf('.')) : "unknown";
    }
}
