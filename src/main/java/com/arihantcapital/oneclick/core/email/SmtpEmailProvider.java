package com.arihantcapital.oneclick.core.email;

import com.arihantcapital.oneclick.config.EmailProperties;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import java.util.Properties;

@Slf4j
@Component
public class SmtpEmailProvider implements EmailProvider {
    private final EmailProperties.SmtpConfig config;
    private final JavaMailSender mailSender;
    private final MeterRegistry meterRegistry;
    private volatile boolean healthy = true;

    public SmtpEmailProvider(EmailProperties.SmtpConfig config, MeterRegistry meterRegistry) {
        this.config = config;
        this.meterRegistry = meterRegistry;
        this.mailSender = createMailSender();
    }

    private JavaMailSender createMailSender() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost(config.getHost());
        mailSender.setPort(config.getPort());
        mailSender.setUsername(config.getUsername());
        mailSender.setPassword(config.getPassword());

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", "smtp");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.putAll(config.getProperties());

        return mailSender;
    }

    @Override
    public void sendEmail(EmailRequest request) throws EmailException {
        Timer.Sample sample = Timer.start(meterRegistry);
        try {
            MimeMessage message = createMimeMessage(request);
            mailSender.send(message);

            meterRegistry.counter("email.sent.success", "provider", getName()).increment();
            log.info("Email sent successfully using provider: {}", getName());

        } catch (Exception e) {
            meterRegistry.counter("email.sent.failure", "provider", getName()).increment();
            healthy = false;
            throw new EmailException("Failed to send email using provider: " + getName(), e);
        } finally {
            sample.stop(Timer.builder("email.send.duration")
                    .tag("provider", getName())
                    .register(meterRegistry));
        }
    }

    private MimeMessage createMimeMessage(EmailRequest request) throws MessagingException, MessagingException {
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

        helper.setFrom(request.getFromAddress(), request.getFromName());
        helper.setTo(request.getRecipients().toArray(new String[0]));
        helper.setSubject(request.getSubject());

        if (request.getCc() != null && !request.getCc().isEmpty()) {
            helper.setCc(request.getCc().toArray(new String[0]));
        }

        if (request.getBcc() != null && !request.getBcc().isEmpty()) {
            helper.setBcc(request.getBcc().toArray(new String[0]));
        }

        if (request.getHtml() != null) {
            helper.setText(request.getHtml(), true);
        } else if (request.getText() != null) {
            helper.setText(request.getText());
        }

        if (request.getAttachments() != null) {
            for (EmailAttachment attachment : request.getAttachments()) {
                helper.addAttachment(attachment.getFilename(), attachment.getDataSource());
            }
        }

        return message;
    }

    @Override
    public boolean isHealthy() {
        return healthy && config.isEnabled();
    }

    @Override
    public void testConnection() throws EmailException {
        try {
            mailSender.testConnection();
            healthy = true;
        } catch (Exception e) {
            healthy = false;
            throw new EmailException("Health check failed for provider: " + getName(), e);
        }
    }

    @Override
    public String getName() {
        return config.getName();
    }

    @Override
    public int getPriority() {
        return config.getPriority();
    }
}
