package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.kra.cvl.pancheck.json.CvlPancheckJsonClientImpl;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("cvlPancheckJsonService")
public class CvlPancheckJsonService {
    private final OneclickProperties.KraConfig.CvlConfig.PancheckConfig.JsonConfig config;
    private final CvlPancheckJsonClientImpl cvlPancheckJsonClient;

    public CvlPancheckJsonService(OneclickProperties.KraConfig.CvlConfig.PancheckConfig.JsonConfig config) {
        this.config = config;

        CvlPancheckJsonClientImpl.ClientConfig clientConfig = new CvlPancheckJsonClientImpl.ClientConfig(
                config.baseUrl(),
                config.userAgent(),
                config.apiKey(),
                config.aesKey(),
                config.username(),
                config.password(),
                config.posCode(),
                config.rtaCode(),
                config.tokenValidTime());
        this.cvlPancheckJsonClient = new CvlPancheckJsonClientImpl(clientConfig);
        log.info("KRA Pancheck JSON Service initialized with client config: {}", clientConfig);
    }

    /**
     * Get Token API - Get Token for further requests
     *
     * @return JsonNode containing the Token response
     */
    public JsonNode getToken(String tokenValidTime) {
        try {
            JsonNode token = cvlPancheckJsonClient.getToken(tokenValidTime);
            log.info("Token Response: {}", token);

            return token;
        } catch (Exception e) {
            log.error("Error fetching Token: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Get PAN Status API - Get PAN Status for further requests
     *
     * @param panNumber Client Pan Number
     * @return JsonNode containing the PAN Status response
     */
    public JsonNode getPanStatus(String panNumber) {
        try {
            String token =
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMDI3ZDczZmU1NTQ0MWVmOTVjOTZjMGNkMjE2MDFjMCIsInVuaXF1ZV9uYW1lIjoiRUtZQyIsIm5iZiI6MTc1MDkyNDgwMCwiZXhwIjoxNzUxMDExMjAwLCJpYXQiOjE3NTA5MjQ4MDAsImlzcyI6Imh0dHA6Ly9sb2NhbGhvc3QiLCJhdWQiOiJodHRwOi8vbG9jYWxob3N0In0.mJ6qGLsRPVIu_RkmCImyis7gq8c51Y9qfrsuDrjCVIg";
            JsonNode response = cvlPancheckJsonClient.getPanStatus(token, panNumber);
            log.info("PAN Status Response: {}", response);
            return response;
        } catch (Exception e) {
            log.error("Error fetching PAN Status: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Solicit PAN Details Fetch ALL KRA API - Solicit PAN Details Fetch ALL KRA for further requests
     *
     * @param panNumber Request json payload
     * @param kraCode   Request json payload
     * @param dob       Request json payload
     * @param fetchType Request json payload
     * @return JsonNode containing the Solicit PAN Details Fetch ALL KRA response
     */
    public JsonNode solicitPANDetailsFetchALLKRA(String panNumber, String kraCode, String dob, String fetchType) {
        try {
            String token =
                    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiIxMDI3ZDczZmU1NTQ0MWVmOTVjOTZjMGNkMjE2MDFjMCIsInVuaXF1ZV9uYW1lIjoiRUtZQyIsIm5iZiI6MTc1MDkyNDgwMCwiZXhwIjoxNzUxMDExMjAwLCJpYXQiOjE3NTA5MjQ4MDAsImlzcyI6Imh0dHA6Ly9sb2NhbGhvc3QiLCJhdWQiOiJodHRwOi8vbG9jYWxob3N0In0.mJ6qGLsRPVIu_RkmCImyis7gq8c51Y9qfrsuDrjCVIg";

            JsonNode response =
                    cvlPancheckJsonClient.solicitPANDetailsFetchALLKRA(token, fetchType, kraCode, panNumber, dob);
            log.info("Solicit PAN Details Fetch ALL KRA Response: {}", response);
            return response;
        } catch (Exception e) {
            log.error("Error fetching Solicit PAN Details Fetch ALL KRA: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Insert/Update KYC Record API - Insert/Update KYC Record for further requests
     *
     * @param payload Request json payload
     * @return JsonNode containing the Insert/Update KYC Record response
     */
    public JsonNode insertUpdateKYCRecord(JsonNode payload) {
        try {
            JsonNode response = cvlPancheckJsonClient.insertUpdateKYCRecord(payload);
            log.info("Insert/Update KYC Record Response: {}", response);
            return response;
        } catch (Exception e) {
            log.error("Error with Insert/Update KYC Record: {}", e.getMessage());
            return null;
        }
    }
}
