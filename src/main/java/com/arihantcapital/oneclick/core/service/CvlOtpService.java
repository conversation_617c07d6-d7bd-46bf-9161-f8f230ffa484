package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("cvlOtpService")
public class CvlOtpService {
    private final OneclickProperties.KraConfig.CvlConfig.OtpConfig config;

    public CvlOtpService(OneclickProperties.KraConfig.CvlConfig.OtpConfig config) {
        this.config = config;
        log.info("KRA OTP Service initialized with base URL: {}", config.baseUrl());
    }

    // Your KRA OTP service methods here
}
