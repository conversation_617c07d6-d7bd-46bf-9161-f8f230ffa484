package com.arihantcapital.oneclick.core.service;

import com.arihantcapital.oneclick.OneclickProperties;
import com.arihantcapital.oneclick.core.client.odin.OdinUumClientImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service(value = "odinUumService")
public class OdinUumService {
    private final OneclickProperties.OdinUumConfig config;
    private OdinUumClientImpl odinUumClient;

    public OdinUumService(OneclickProperties.OdinUumConfig config) {
        this.config = config;
        OdinUumClientImpl.ClientConfig clientConfig = new OdinUumClientImpl.ClientConfig(config);
        this.odinUumClient = new OdinUumClientImpl(clientConfig);
        log.info("ODIN UUM Service initialized with base URL: {}", config.baseUrl());
    }
}
