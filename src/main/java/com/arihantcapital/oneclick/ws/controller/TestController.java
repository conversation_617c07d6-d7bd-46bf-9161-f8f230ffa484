package com.arihantcapital.oneclick.ws.controller;

import com.arihantcapital.oneclick.utils.CSVHelper;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/api/test")
public class TestController {

    @PostMapping("/upload-csv")
    public ResponseEntity<String> uploadCSV(@RequestParam("file") MultipartFile file) {
        try {
            if (!CSVHelper.hasCSVFormat(file)) {
                return ResponseEntity.badRequest().body("Please upload a CSV file!");
            }

            List<Map<String, String>> csvData = CSVHelper.readCSVFromMultipartFile(file);

            System.out.println("CSV Data: " + csvData);

            // Process your data
            csvData.forEach(row -> {
                System.out.println("Row: " + row);
            });

            return ResponseEntity.ok("CSV processed successfully!");
        } catch (IOException e) {
            return ResponseEntity.status(500).body("Error processing CSV: " + e.getMessage());
        }
    }
}
