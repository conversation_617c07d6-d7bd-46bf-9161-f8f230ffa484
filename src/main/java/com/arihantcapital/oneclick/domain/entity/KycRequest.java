package com.arihantcapital.oneclick.domain.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Entity
@Table(name = "kyc_requests")
@Getter
@Setter
public class KycRequest {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "identifier", unique = true, nullable = false)
    private String identifier;

    @Column(name = "client_code", unique = true, nullable = false)
    private String clientCode;

    @Column(name = "kyc_flow", nullable = false)
    private String kycFlow;

    @Column(name = "activities", nullable = false)
    private String activities;

    @Column(name = "source", nullable = false)
    private String source;

    @Column(name = "validation_status", nullable = false)
    private String validationStatus;

    @Column(name = "validation_message", nullable = false)
    private String validationMessage;

    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    @PrePersist
    public void prePersist() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    public void preUpdate() {
        updatedAt = Instant.now();
    }
}
