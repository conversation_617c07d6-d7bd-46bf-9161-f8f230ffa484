package com.arihantcapital.oneclick.domain.entity.log.email;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcType;
import org.hibernate.dialect.PostgreSQLEnumJdbcType;

import java.time.Instant;
import java.time.LocalDateTime;

@Entity
@Table(name = "email_delivery_logs")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailDeliveryLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "recipients", columnDefinition = "TEXT")
    private String recipients;

    @Column(name = "subject")
    private String subject;

    @Column(name = "template_name")
    private String templateName;

    @Enumerated(EnumType.STRING)
    @JdbcType(PostgreSQLEnumJdbcType.class)
    @Column(name = "status")
    private EmailStatus status;

    @Column(name = "provider")
    private String provider;

    @Column(name = "attempts")
    private Integer attempts;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "sent_at")
    private LocalDateTime sentAt;

    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    @PrePersist
    public void prePersist() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    public void preUpdate() {
        updatedAt = Instant.now();
    }

}
//CREATE TABLE email_delivery_logs (
//        id BIGINT AUTO_INCREMENT PRIMARY KEY,
//        recipients TEXT,
//        subject VARCHAR(255),
//template_name VARCHAR(100),
//status VARCHAR(20),
//provider VARCHAR(50),
//attempts INT,
//error_message TEXT,
//created_at TIMESTAMP,
//sent_at TIMESTAMP
//);