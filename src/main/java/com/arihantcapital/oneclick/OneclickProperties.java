package com.arihantcapital.oneclick;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@Validated
@ConfigurationProperties(prefix = "oneclick")
public record OneclickProperties(
        @Valid CkycConfig ckyc,
        @Valid KraConfig kra,
        @Valid NseUccConfig nseUcc,
        @Valid BseUccConfig bseUcc,
        @Valid BseStarMutualFundConfig bseStarMutualFund,
        @Valid KorpConfig korp,
        @Valid MsilConfig msil,
        @Valid OdinUumConfig odinUum) {

    public record KorpConfig(
            @NotNull Boolean enabled,
            // Properties required only if enabled
            String baseUrl,
            String username,
            String password,
            String grantType) {
        // Custom validation - properties required only if enabled
        public KorpConfig {
            if (Boolean.TRUE.equals(enabled)) {
                validateRequiredField(baseUrl, "baseUrl");
                validate<PERSON>equired<PERSON><PERSON>(username, "username");
                validate<PERSON>equired<PERSON><PERSON>(password, "password");
                validateRequired<PERSON>ield(grantType, "grantType");
            }
        }

        private void validateRequiredField(String value, String fieldName) {
            if (value == null || value.trim().isEmpty()) {
                throw new IllegalArgumentException(
                        String.format("Korp is enabled but required field '%s' is missing or empty", fieldName));
            }
        }
    }

    public record MsilConfig(
            @NotNull Boolean enabled,
            // Properties required only if enabled
            String baseUrl,
            String token) {
        // Custom validation - properties required only if enabled
        public MsilConfig {
            if (Boolean.TRUE.equals(enabled)) {
                validateRequiredField(baseUrl, "baseUrl");
                validateRequiredField(token, "token");
            }
        }

        private void validateRequiredField(String value, String fieldName) {
            if (value == null || value.trim().isEmpty()) {
                throw new IllegalArgumentException(
                        String.format("MSIL is enabled but required field '%s' is missing or empty", fieldName));
            }
        }
    }

    public record CkycConfig(
            @NotNull Boolean enabled,
            // Properties required only if enabled
            String apiVersion,
            String fiCode,
            String baseUrl,
            String keyStoreType,
            String cersaiPublicKeyFilePath,
            String fiCertificateKeyStoreFilePath,
            String fiCertificateKeyStoreFilePassword,
            String fiCertificatePrivateKeyAlias) {
        // Custom validation - properties required only if enabled
        public CkycConfig {
            if (Boolean.TRUE.equals(enabled)) {
                validateRequiredField(apiVersion, "apiVersion");
                validateRequiredField(fiCode, "fiCode");
                validateRequiredField(baseUrl, "baseUrl");
                validateRequiredField(keyStoreType, "keyStoreType");
                validateRequiredField(cersaiPublicKeyFilePath, "cersaiPublicKeyFilePath");
                validateRequiredField(fiCertificateKeyStoreFilePath, "fiCertificateKeyStoreFilePath");
                validateRequiredField(fiCertificateKeyStoreFilePassword, "fiCertificateKeyStoreFilePassword");
                validateRequiredField(fiCertificatePrivateKeyAlias, "fiCertificatePrivateKeyAlias");
            }
        }

        private void validateRequiredField(String value, String fieldName) {
            if (value == null || value.trim().isEmpty()) {
                throw new IllegalArgumentException(
                        String.format("CKYC is enabled but required field '%s' is missing or empty", fieldName));
            }
        }
    }

    public record BseStarMutualFundConfig(
            @NotNull Boolean enabled,
            // Properties required only if enabled
            String baseUrl,
            String memberCode,
            String username,
            String password) {
        // Custom validation - properties required only if enabled
        public BseStarMutualFundConfig {
            if (Boolean.TRUE.equals(enabled)) {
                validateRequiredField(baseUrl, "baseUrl");
                validateRequiredField(memberCode, "memberCode");
                validateRequiredField(username, "username");
                validateRequiredField(password, "password");
            }
        }

        private void validateRequiredField(String value, String fieldName) {
            if (value == null || value.trim().isEmpty()) {
                throw new IllegalArgumentException(String.format(
                        "BSE Star Mutual Fund is enabled but required field '%s' is missing or empty", fieldName));
            }
        }
    }

    public record NseUccConfig(
            @NotNull Boolean enabled,
            // Properties required only if enabled
            String baseUrl,
            String username,
            String password,
            String apiName) {
        // Custom validation - properties required only if enabled
        public NseUccConfig {
            if (Boolean.TRUE.equals(enabled)) {
                validateRequiredField(baseUrl, "baseUrl");
                validateRequiredField(username, "username");
                validateRequiredField(password, "password");
                validateRequiredField(apiName, "apiName");
            }
        }

        private void validateRequiredField(String value, String fieldName) {
            if (value == null || value.trim().isEmpty()) {
                throw new IllegalArgumentException(
                        String.format("NSE UCC is enabled but required field '%s' is missing or empty", fieldName));
            }
        }
    }

    public record BseUccConfig(
            @NotNull Boolean enabled,
            // Properties required only if enabled
            String apiVersion,
            String baseUrl,
            String memberCode,
            String username,
            String password,
            String publicKeyXml) {
        // Custom validation - properties required only if enabled
        public BseUccConfig {
            if (Boolean.TRUE.equals(enabled)) {
                validateRequiredField(apiVersion, "apiVersion");
                validateRequiredField(baseUrl, "baseUrl");
                validateRequiredField(memberCode, "memberCode");
                validateRequiredField(username, "username");
                validateRequiredField(password, "password");
                validateRequiredField(publicKeyXml, "publicKeyXml");
            }
        }

        private void validateRequiredField(String value, String fieldName) {
            if (value == null || value.trim().isEmpty()) {
                throw new IllegalArgumentException(
                        String.format("BSE UCC is enabled but required field '%s' is missing or empty", fieldName));
            }
        }
    }

    public record KraConfig(@Valid CvlConfig cvl) {

        public record CvlConfig(@Valid OtpConfig otp, @Valid PancheckConfig pancheck, @Valid SftpConfig sftp) {

            public record OtpConfig(
                    @NotNull Boolean enabled,
                    // Properties required only if enabled
                    String baseUrl,
                    String username,
                    String password) {
                // Custom validation - properties required only if enabled
                public OtpConfig {
                    if (Boolean.TRUE.equals(enabled)) {
                        validateRequiredField(baseUrl, "baseUrl");
                        validateRequiredField(username, "username");
                        validateRequiredField(password, "password");
                    }
                }

                private void validateRequiredField(String value, String fieldName) {
                    if (value == null || value.trim().isEmpty()) {
                        throw new IllegalArgumentException(String.format(
                                "KRA CVL OTP is enabled but required field '%s' is missing or empty", fieldName));
                    }
                }
            }

            public record PancheckConfig(@Valid XmlConfig xml, @Valid JsonConfig json) {

                public record XmlConfig(
                        @NotNull Boolean enabled,
                        // Properties required only if enabled
                        String baseUrl,
                        String username,
                        String password,
                        String posCode,
                        String rtaCode) {
                    // Custom validation - properties required only if enabled
                    public XmlConfig {
                        if (Boolean.TRUE.equals(enabled)) {
                            validateRequiredField(baseUrl, "baseUrl");
                            validateRequiredField(username, "username");
                            validateRequiredField(password, "password");
                            validateRequiredField(posCode, "posCode");
                            validateRequiredField(rtaCode, "rtaCode");
                        }
                    }

                    private void validateRequiredField(String value, String fieldName) {
                        if (value == null || value.trim().isEmpty()) {
                            throw new IllegalArgumentException(String.format(
                                    "KRA CVL Pancheck XML is enabled but required field '%s' is missing or empty",
                                    fieldName));
                        }
                    }
                }

                public record JsonConfig(
                        @NotNull Boolean enabled,
                        // Properties required only if enabled
                        String userAgent,
                        String baseUrl,
                        String apiKey,
                        String aesKey,
                        String username,
                        String password,
                        String posCode,
                        String rtaCode,
                        String tokenValidTime) {
                    // Custom validation - properties required only if enabled
                    public JsonConfig {
                        if (Boolean.TRUE.equals(enabled)) {
                            validateRequiredField(userAgent, "userAgent");
                            validateRequiredField(baseUrl, "baseUrl");
                            validateRequiredField(apiKey, "apiKey");
                            validateRequiredField(aesKey, "aesKey");
                            validateRequiredField(username, "username");
                            validateRequiredField(password, "password");
                            validateRequiredField(posCode, "posCode");
                            validateRequiredField(rtaCode, "rtaCode");
                            validateRequiredField(tokenValidTime, "tokenValidTime");
                        }
                    }

                    private void validateRequiredField(String value, String fieldName) {
                        if (value == null || value.trim().isEmpty()) {
                            throw new IllegalArgumentException(String.format(
                                    "KRA CVL Pancheck JSON is enabled but required field '%s' is missing or empty",
                                    fieldName));
                        }
                    }
                }
            }

            public record SftpConfig(
                    @NotNull Boolean enabled,
                    // Properties required only if enabled
                    String host,
                    String port,
                    String username,
                    String password) {
                // Custom validation - properties required only if enabled
                public SftpConfig {
                    if (Boolean.TRUE.equals(enabled)) {
                        validateRequiredField(host, "host");
                        validateRequiredField(port, "port");
                        validateRequiredField(username, "username");
                        validateRequiredField(password, "password");
                    }
                }

                private void validateRequiredField(String value, String fieldName) {
                    if (value == null || value.trim().isEmpty()) {
                        throw new IllegalArgumentException(String.format(
                                "KRA CVL SFTP is enabled but required field '%s' is missing or empty", fieldName));
                    }
                }
            }
        }
    }

    public record OdinUumConfig(
            @NotNull Boolean enabled,
            // Properties required only if enabled
            String baseUrl,
            String username,
            String password,
            String ipAddress) {
        // Custom validation - properties required only if enabled
        public OdinUumConfig {
            if (Boolean.TRUE.equals(enabled)) {
                validateRequiredField(baseUrl, "baseUrl");
                validateRequiredField(username, "username");
                validateRequiredField(password, "password");
                validateRequiredField(ipAddress, "ipAddress");
            }
        }

        private void validateRequiredField(String value, String fieldName) {
            if (value == null || value.trim().isEmpty()) {
                throw new IllegalArgumentException(
                        String.format("ODIN UUM is enabled but required field '%s' is missing or empty", fieldName));
            }
        }
    }

    // Utility methods for checking if services are enabled

    public boolean isKorpEnabled() {
        return Boolean.TRUE.equals(korp.enabled());
    }

    public boolean isMsilEnabled() {
        return Boolean.TRUE.equals(msil.enabled());
    }

    public boolean isNseUccEnabled() {
        return Boolean.TRUE.equals(nseUcc.enabled());
    }

    public boolean isBseUccEnabled() {
        return Boolean.TRUE.equals(bseUcc.enabled());
    }

    public boolean isBseStarMutualFundEnabled() {
        return Boolean.TRUE.equals(bseStarMutualFund.enabled());
    }

    public boolean isCkycEnabled() {
        return Boolean.TRUE.equals(ckyc.enabled());
    }

    public boolean isKraOtpEnabled() {
        return Boolean.TRUE.equals(kra.cvl().otp().enabled());
    }

    public boolean isKraPancheckXmlEnabled() {
        return Boolean.TRUE.equals(kra.cvl().pancheck().xml().enabled());
    }

    public boolean isKraPancheckJsonEnabled() {
        return Boolean.TRUE.equals(kra.cvl().pancheck().json().enabled());
    }

    public boolean isOdinUumEnabled() {
        return Boolean.TRUE.equals(odinUum.enabled());
    }

    public boolean isCvlKraSftpEnabled() {
        return Boolean.TRUE.equals(kra.cvl().sftp().enabled());
    }
}
