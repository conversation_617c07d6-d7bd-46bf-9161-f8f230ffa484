package com.arihantcapital.oneclick.service;

import com.arihantcapital.oneclick.core.dto.korp.KorpBranchDetails;
import com.arihantcapital.oneclick.core.dto.korp.KorpClientDetails;
import com.arihantcapital.oneclick.core.service.KorpService;
import com.arihantcapital.oneclick.domain.entity.Branch;
import com.arihantcapital.oneclick.domain.entity.Client;
import com.arihantcapital.oneclick.domain.repository.BranchRepository;
import com.arihantcapital.oneclick.domain.repository.ClientRepository;
import com.arihantcapital.oneclick.core.mapper.OneclickKorpMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service(value = "clientService")
public class ClientService {

    @Autowired
    private KorpService korpService;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private BranchRepository branchRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public List<Client> getClients() {
        return clientRepository.findAll();
    }

    public void syncClient(String clientCode) {
        try {
            KorpClientDetails clientMaster =
                    objectMapper.convertValue(korpService.getClientMaster(clientCode), KorpClientDetails.class);
            KorpBranchDetails branchDetails = objectMapper.convertValue(
                    korpService
                            .getBranchMaster(
                                    clientMaster.getKycDetail().getFirst().getBranchId())
                            .get(0),
                    KorpBranchDetails.class);

            Client client = OneclickKorpMapper.getClient(clientMaster);
            Branch branch = OneclickKorpMapper.getBranch(branchDetails);

            clientRepository.save(client);
            branchRepository.save(branch);

            log.info("Client Synced Successfully");

        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
