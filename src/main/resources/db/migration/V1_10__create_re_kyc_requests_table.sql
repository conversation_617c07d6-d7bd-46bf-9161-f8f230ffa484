-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_10__create_re_kyc_requests_table.sql

CREATE TABLE re_kyc_requests (
    id BIGSERIAL PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL UNIQUE,
    client_code VARCHAR(10),
    re_kyc_type VARCHAR(50),
    reason VARCHAR(255),
    priority VARCHAR(20),
    due_date DATE,
    status VARCHAR(50),
    assigned_to VARCHAR(100),
    remarks TEXT,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Add foreign key constraint
ALTER TABLE re_kyc_requests ADD CONSTRAINT fk_re_kyc_requests_client_code
FOREIGN KEY (client_code) REFERENCES clients(client_code);

-- Create indexes for better performance
CREATE INDEX idx_re_kyc_requests_identifier ON re_kyc_requests(identifier);
CREATE INDEX idx_re_kyc_requests_client_code ON re_kyc_requests(client_code);
CREATE INDEX idx_re_kyc_requests_re_kyc_type ON re_kyc_requests(re_kyc_type);
CREATE INDEX idx_re_kyc_requests_status ON re_kyc_requests(status);
CREATE INDEX idx_re_kyc_requests_priority ON re_kyc_requests(priority);
CREATE INDEX idx_re_kyc_requests_due_date ON re_kyc_requests(due_date);
CREATE INDEX idx_re_kyc_requests_assigned_to ON re_kyc_requests(assigned_to);
CREATE INDEX idx_re_kyc_requests_created_at ON re_kyc_requests(created_at);
CREATE INDEX idx_re_kyc_requests_updated_at ON re_kyc_requests(updated_at);

-- Add comments for documentation
COMMENT ON TABLE re_kyc_requests IS 'Table storing re-KYC request information for periodic client verification';
COMMENT ON COLUMN re_kyc_requests.id IS 'Primary key - unique identifier for each re-KYC request';
COMMENT ON COLUMN re_kyc_requests.identifier IS 'Unique identifier for the re-KYC request';
COMMENT ON COLUMN re_kyc_requests.client_code IS 'Client code associated with this re-KYC request';
COMMENT ON COLUMN re_kyc_requests.re_kyc_type IS 'Type of re-KYC required (periodic/risk-based/regulatory/etc)';
COMMENT ON COLUMN re_kyc_requests.reason IS 'Reason for initiating the re-KYC process';
COMMENT ON COLUMN re_kyc_requests.priority IS 'Priority level of the re-KYC request (high/medium/low)';
COMMENT ON COLUMN re_kyc_requests.due_date IS 'Due date for completing the re-KYC process';
COMMENT ON COLUMN re_kyc_requests.status IS 'Current status of the re-KYC request (initiated/in-progress/completed/expired)';
COMMENT ON COLUMN re_kyc_requests.assigned_to IS 'User or team assigned to handle this re-KYC request';
COMMENT ON COLUMN re_kyc_requests.remarks IS 'Additional remarks or notes about the re-KYC request';
COMMENT ON COLUMN re_kyc_requests.created_at IS 'Timestamp when the re-KYC request was created';
COMMENT ON COLUMN re_kyc_requests.updated_at IS 'Timestamp when the re-KYC request was last updated';
