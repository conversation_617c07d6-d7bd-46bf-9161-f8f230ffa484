-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_4__create_clients_table.sql

CREATE TABLE clients (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    kyc_mode VARCHAR(10),
    client_code VARCHAR(10) NOT NULL UNIQUE,
    client_name VARCHA<PERSON>(255),
    pan_number VARCHAR(10),
    name_as_per_pan VARCHAR(255),
    client_type VARCHAR(50),
    client_sub_type VARCHAR(50),
    branch_code VARCHAR(50),
    group_code VARCHAR(50),
    email VARCHAR(255),
    mobile VARCHAR(20),
    dob_or_doi VARCHAR(50),
    gender VARCHAR(20),
    marital_status VARCHAR(50),
    father_or_spouse_name VARCHA<PERSON>(255),
    kra_code VARCHAR(50),
    segments JSON,
    client_status VARCHAR(50),
    opted_for_upi VARCHAR(10),
    ddpi VARCHAR(10),
    ddpi_date VARCHAR(50),
    poa VARCHAR(10),
    poa_date VARCHAR(50),
    agreement_date VARCHAR(50),
    ckyc_ref_no VARCHAR(100),
    pep VARCHAR(10),
    income_range VARCHAR(50),
    income_date VARCHAR(50),
    address_proof VARCHAR(100),
    address_proof_ref_no VARCHAR(100),
    address1 VARCHAR(255),
    address2 VARCHAR(255),
    address3 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    pincode VARCHAR(20),
    nationality VARCHAR(100),
    residential_status VARCHAR(50),
    occupation VARCHAR(100),
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_clients_client_code ON clients(client_code);
CREATE INDEX idx_clients_pan_number ON clients(pan_number);
CREATE INDEX idx_clients_email ON clients(email);
CREATE INDEX idx_clients_mobile ON clients(mobile);
CREATE INDEX idx_clients_branch_code ON clients(branch_code);
CREATE INDEX idx_clients_group_code ON clients(group_code);
CREATE INDEX idx_clients_client_status ON clients(client_status);
CREATE INDEX idx_clients_client_type ON clients(client_type);
CREATE INDEX idx_clients_kra_code ON clients(kra_code);
CREATE INDEX idx_clients_created_at ON clients(created_at);

-- Add comments for documentation
ALTER TABLE clients COMMENT = 'Table storing client information and KYC details';
ALTER TABLE clients MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT 'Primary key - unique identifier for each client';
ALTER TABLE clients MODIFY COLUMN kyc_mode VARCHAR(50) COMMENT 'KYC mode used for client onboarding';
ALTER TABLE clients MODIFY COLUMN client_code VARCHAR(50) NOT NULL UNIQUE COMMENT 'Unique code identifying the client';
ALTER TABLE clients MODIFY COLUMN client_name VARCHAR(255) COMMENT 'Full name of the client';
ALTER TABLE clients MODIFY COLUMN pan_number VARCHAR(20) COMMENT 'PAN number of the client';
ALTER TABLE clients MODIFY COLUMN name_as_per_pan VARCHAR(255) COMMENT 'Name as it appears on PAN card';
ALTER TABLE clients MODIFY COLUMN client_type VARCHAR(50) COMMENT 'Type of client (individual/corporate/etc)';
ALTER TABLE clients MODIFY COLUMN client_sub_type VARCHAR(50) COMMENT 'Sub-type classification of client';
ALTER TABLE clients MODIFY COLUMN branch_code VARCHAR(50) COMMENT 'Branch code where client is registered';
ALTER TABLE clients MODIFY COLUMN group_code VARCHAR(50) COMMENT 'Group code for client classification';
ALTER TABLE clients MODIFY COLUMN email VARCHAR(255) COMMENT 'Email address of the client';
ALTER TABLE clients MODIFY COLUMN mobile VARCHAR(20) COMMENT 'Mobile number of the client';
ALTER TABLE clients MODIFY COLUMN dob_or_doi VARCHAR(50) COMMENT 'Date of birth for individual or date of incorporation for corporate';
ALTER TABLE clients MODIFY COLUMN gender VARCHAR(20) COMMENT 'Gender of the client';
ALTER TABLE clients MODIFY COLUMN marital_status VARCHAR(50) COMMENT 'Marital status of the client';
ALTER TABLE clients MODIFY COLUMN father_or_spouse_name VARCHAR(255) COMMENT 'Father or spouse name';
ALTER TABLE clients MODIFY COLUMN kra_code VARCHAR(50) COMMENT 'KRA (KYC Registration Agency) code';
ALTER TABLE clients MODIFY COLUMN segments JSON COMMENT 'Trading segments enabled for client (stored as JSON array)';
ALTER TABLE clients MODIFY COLUMN client_status VARCHAR(50) COMMENT 'Current status of the client account';
ALTER TABLE clients MODIFY COLUMN opted_for_upi VARCHAR(10) COMMENT 'Whether client opted for UPI payments';
ALTER TABLE clients MODIFY COLUMN ddpi VARCHAR(10) COMMENT 'DDPI (Demat Debit and Pledge Instruction) status';
ALTER TABLE clients MODIFY COLUMN ddpi_date VARCHAR(50) COMMENT 'Date of DDPI registration';
ALTER TABLE clients MODIFY COLUMN poa VARCHAR(10) COMMENT 'Power of Attorney status';
ALTER TABLE clients MODIFY COLUMN poa_date VARCHAR(50) COMMENT 'Date of POA registration';
ALTER TABLE clients MODIFY COLUMN agreement_date VARCHAR(50) COMMENT 'Date of client agreement';
ALTER TABLE clients MODIFY COLUMN ckyc_ref_no VARCHAR(100) COMMENT 'Central KYC reference number';
ALTER TABLE clients MODIFY COLUMN pep VARCHAR(10) COMMENT 'Politically Exposed Person flag';
ALTER TABLE clients MODIFY COLUMN income_range VARCHAR(50) COMMENT 'Income range of the client';
ALTER TABLE clients MODIFY COLUMN income_date VARCHAR(50) COMMENT 'Date of income declaration';
ALTER TABLE clients MODIFY COLUMN address_proof VARCHAR(100) COMMENT 'Type of address proof provided';
ALTER TABLE clients MODIFY COLUMN address_proof_ref_no VARCHAR(100) COMMENT 'Reference number of address proof';
ALTER TABLE clients MODIFY COLUMN address1 VARCHAR(255) COMMENT 'Address line 1';
ALTER TABLE clients MODIFY COLUMN address2 VARCHAR(255) COMMENT 'Address line 2';
ALTER TABLE clients MODIFY COLUMN address3 VARCHAR(255) COMMENT 'Address line 3';
ALTER TABLE clients MODIFY COLUMN city VARCHAR(100) COMMENT 'City';
ALTER TABLE clients MODIFY COLUMN state VARCHAR(100) COMMENT 'State';
ALTER TABLE clients MODIFY COLUMN country VARCHAR(100) COMMENT 'Country';
ALTER TABLE clients MODIFY COLUMN pincode VARCHAR(20) COMMENT 'PIN/ZIP code';
ALTER TABLE clients MODIFY COLUMN nationality VARCHAR(100) COMMENT 'Nationality of the client';
ALTER TABLE clients MODIFY COLUMN residential_status VARCHAR(50) COMMENT 'Residential status (resident/non-resident)';
ALTER TABLE clients MODIFY COLUMN occupation VARCHAR(100) COMMENT 'Occupation of the client';
ALTER TABLE clients MODIFY COLUMN created_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was created';
ALTER TABLE clients MODIFY COLUMN updated_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was last updated';