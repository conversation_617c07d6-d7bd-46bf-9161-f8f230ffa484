-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_7__create_client_nominees_table.sql

CREATE TABLE client_nominees (
    id BIGSERIAL PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL UNIQUE,
    client_code VA<PERSON>HAR(10),
    name <PERSON><PERSON><PERSON><PERSON>(255),
    relationship VARCHAR(50),
    dob <PERSON><PERSON><PERSON><PERSON>(50),
    share_percentage DECIMAL(5,2),
    minor_nominee VARCHAR(10),
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Add foreign key constraint
ALTER TABLE client_nominees ADD CONSTRAINT fk_client_nominees_client_code
FOREIGN KEY (client_code) REFERENCES clients(client_code);

-- Create indexes for better performance
CREATE INDEX idx_client_nominees_identifier ON client_nominees(identifier);
CREATE INDEX idx_client_nominees_client_code ON client_nominees(client_code);
CREATE INDEX idx_client_nominees_name ON client_nominees(name);
CREATE INDEX idx_client_nominees_relationship ON client_nominees(relationship);
CREATE INDEX idx_client_nominees_minor_nominee ON client_nominees(minor_nominee);
CREATE INDEX idx_client_nominees_created_at ON client_nominees(created_at);

-- Add comments for documentation
COMMENT ON TABLE client_nominees IS 'Table storing client nominee information';
COMMENT ON COLUMN client_nominees.id IS 'Primary key - unique identifier for each client nominee record';
COMMENT ON COLUMN client_nominees.client_code IS 'Client code associated with this nominee';
COMMENT ON COLUMN client_nominees.name IS 'Full name of the nominee';
COMMENT ON COLUMN client_nominees.relationship IS 'Relationship of nominee with the client (father/mother/spouse/child/etc)';
COMMENT ON COLUMN client_nominees.dob IS 'Date of birth of the nominee';
COMMENT ON COLUMN client_nominees.share_percentage IS 'Percentage share of nomination (0.00 to 100.00)';
COMMENT ON COLUMN client_nominees.minor_nominee IS 'Flag indicating if nominee is a minor (Y/N)';
COMMENT ON COLUMN client_nominees.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN client_nominees.updated_at IS 'Timestamp when the record was last updated';