-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_7__create_client_nominees_table.sql

CREATE TABLE client_nominees (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    client_code VARCHAR(10),
    name <PERSON><PERSON><PERSON><PERSON>(255),
    relationship VARCHAR(50),
    dob <PERSON>(50),
    share_percentage DECIMAL(5,2),
    minor_nominee VARCHAR(10),
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Add foreign key constraint
ALTER TABLE client_nominees ADD CONSTRAINT fk_client_nominees_client_code
FOREIGN KEY (client_code) REFERENCES clients(client_code);

-- Create indexes for better performance
CREATE INDEX idx_client_nominees_client_code ON client_nominees(client_code);
CREATE INDEX idx_client_nominees_name ON client_nominees(name);
CREATE INDEX idx_client_nominees_relationship ON client_nominees(relationship);
CREATE INDEX idx_client_nominees_minor_nominee ON client_nominees(minor_nominee);
CREATE INDEX idx_client_nominees_created_at ON client_nominees(created_at);

-- Add comments for documentation
ALTER TABLE client_nominees COMMENT = 'Table storing client nominee information';
ALTER TABLE client_nominees MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT 'Primary key - unique identifier for each client nominee record';
ALTER TABLE client_nominees MODIFY COLUMN client_code VARCHAR(50) COMMENT 'Client code associated with this nominee';
ALTER TABLE client_nominees MODIFY COLUMN name VARCHAR(255) COMMENT 'Full name of the nominee';
ALTER TABLE client_nominees MODIFY COLUMN relationship VARCHAR(50) COMMENT 'Relationship of nominee with the client (father/mother/spouse/child/etc)';
ALTER TABLE client_nominees MODIFY COLUMN dob VARCHAR(50) COMMENT 'Date of birth of the nominee';
ALTER TABLE client_nominees MODIFY COLUMN share_percentage DECIMAL(5,2) COMMENT 'Percentage share of nomination (0.00 to 100.00)';
ALTER TABLE client_nominees MODIFY COLUMN minor_nominee VARCHAR(10) COMMENT 'Flag indicating if nominee is a minor (Y/N)';
ALTER TABLE client_nominees MODIFY COLUMN created_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was created';
ALTER TABLE client_nominees MODIFY COLUMN updated_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was last updated';