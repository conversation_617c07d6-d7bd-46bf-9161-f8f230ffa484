-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_6__create_client_depositories_table.sql

CREATE TABLE client_depositories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL UNIQUE,
    client_code VARCHAR(10) NOT NULL,
    depository_name VARCHAR(100),
    depository_id VARCHAR(50),
    depository_type VARCHAR(4),
    depository_client_id VARCHAR(50),
    depository_status VARCHAR(50),
    ddpi VARCHAR(10),
    poa VARCHAR(10),
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Add foreign key constraint
ALTER TABLE client_depositories ADD CONSTRAINT fk_client_depositories_client_code
FOREIGN KEY (client_code) REFERENCES clients(client_code);

-- Create indexes for better performance
CREATE INDEX idx_client_depositories_identifier ON client_depositories(identifier);
CREATE INDEX idx_client_depositories_client_code ON client_depositories(client_code);
CREATE INDEX idx_client_depositories_depository_id ON client_depositories(depository_id);
CREATE INDEX idx_client_depositories_depository_client_id ON client_depositories(depository_client_id);
CREATE INDEX idx_client_depositories_depository_status ON client_depositories(depository_status);
CREATE INDEX idx_client_depositories_depository_type ON client_depositories(depository_type);
CREATE INDEX idx_client_depositories_ddpi ON client_depositories(ddpi);
CREATE INDEX idx_client_depositories_poa ON client_depositories(poa);
CREATE INDEX idx_client_depositories_created_at ON client_depositories(created_at);

-- Add comments for documentation
ALTER TABLE client_depositories COMMENT = 'Table storing client depository account information';
ALTER TABLE client_depositories MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT 'Primary key - unique identifier for each client depository record';
ALTER TABLE client_depositories MODIFY COLUMN identifier VARCHAR(100) NOT NULL UNIQUE COMMENT 'Unique identifier for the client depository record';
ALTER TABLE client_depositories MODIFY COLUMN client_code VARCHAR(50) NOT NULL COMMENT 'Client code associated with this depository account';
ALTER TABLE client_depositories MODIFY COLUMN depository_name VARCHAR(100) COMMENT 'Name of the depository (NSDL/CDSL/etc)';
ALTER TABLE client_depositories MODIFY COLUMN depository_id VARCHAR(50) COMMENT 'Depository identifier/code';
ALTER TABLE client_depositories MODIFY COLUMN depository_type VARCHAR(50) COMMENT 'Type of depository account';
ALTER TABLE client_depositories MODIFY COLUMN depository_client_id VARCHAR(50) COMMENT 'Client ID in the depository system';
ALTER TABLE client_depositories MODIFY COLUMN depository_status VARCHAR(50) COMMENT 'Status of the depository account (active/inactive/etc)';
ALTER TABLE client_depositories MODIFY COLUMN ddpi VARCHAR(10) COMMENT 'DDPI (Demat Debit and Pledge Instruction) status';
ALTER TABLE client_depositories MODIFY COLUMN poa VARCHAR(10) COMMENT 'Power of Attorney status';
ALTER TABLE client_depositories MODIFY COLUMN created_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was created';
ALTER TABLE client_depositories MODIFY COLUMN updated_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was last updated';