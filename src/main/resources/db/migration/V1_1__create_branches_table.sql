-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_1__create_branches_table.sql

CREATE TABLE branches (
    id BIGSERIAL PRIMARY KEY,
    branch_code VARCHAR(50) NOT NULL UNIQUE,
    branch_name VA<PERSON>HAR(255) NOT NULL,
    group_code VA<PERSON>HAR(50) NOT NULL,
    region_code VARCHAR(50) NOT NULL,
    branch_mode VARCHAR(50) NOT NULL,
    branch_status VARCHAR(50) NOT NULL,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_branches_branch_code ON branches(branch_code);
CREATE INDEX idx_branches_group_code ON branches(group_code);
CREATE INDEX idx_branches_region_code ON branches(region_code);
CREATE INDEX idx_branches_branch_status ON branches(branch_status);
CREATE INDEX idx_branches_created_at ON branches(created_at);

-- Add comments for documentation
COMMENT ON TABLE branches IS 'Table storing branch information for the organization';
COMMENT ON COLUMN branches.id IS 'Primary key - unique identifier for each branch';
COMMENT ON COLUMN branches.branch_code IS 'Unique code identifying the branch';
COMMENT ON COLUMN branches.branch_name IS 'Display name of the branch';
COMMENT ON COLUMN branches.group_code IS 'Code identifying the group this branch belongs to';
COMMENT ON COLUMN branches.region_code IS 'Code identifying the region this branch belongs to';
COMMENT ON COLUMN branches.branch_mode IS 'Operating mode of the branch';
COMMENT ON COLUMN branches.branch_status IS 'Current status of the branch (active/inactive/etc)';
COMMENT ON COLUMN branches.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN branches.updated_at IS 'Timestamp when the record was last updated';