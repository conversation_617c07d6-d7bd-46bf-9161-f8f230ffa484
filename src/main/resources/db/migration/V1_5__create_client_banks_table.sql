-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_5__create_client_banks_table.sql

CREATE TABLE client_banks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL UNIQUE,
    client_code VA<PERSON>HAR(50),
    bank_name VA<PERSON>HAR(255),
    bank_branch VARCHAR(255),
    bank_account_type VARCHAR(50),
    bank_account_number VARCHAR(50),
    bank_ifsc_code VARCHAR(20),
    bank_micr_code VARCHAR(20),
    bank_code VARCHAR(50),
    bank_address TEXT,
    default_bank VARCHAR(10),
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Add foreign key constraint
ALTER TABLE client_banks ADD CONSTRAINT fk_client_banks_client_code
FOREIGN KEY (client_code) REFERENCES clients(client_code);

-- Create indexes for better performance
CREATE INDEX idx_client_banks_identifier ON client_banks(identifier);
CREATE INDEX idx_client_banks_client_code ON client_banks(client_code);
CREATE INDEX idx_client_banks_bank_account_number ON client_banks(bank_account_number);
CREATE INDEX idx_client_banks_bank_ifsc_code ON client_banks(bank_ifsc_code);
CREATE INDEX idx_client_banks_bank_code ON client_banks(bank_code);
CREATE INDEX idx_client_banks_default_bank ON client_banks(default_bank);
CREATE INDEX idx_client_banks_created_at ON client_banks(created_at);


-- Add comments for documentation
ALTER TABLE client_banks COMMENT = 'Table storing client bank account information';
ALTER TABLE client_banks MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT 'Primary key - unique identifier for each client bank record';
ALTER TABLE client_banks MODIFY COLUMN identifier VARCHAR(100) NOT NULL UNIQUE COMMENT 'Unique identifier for the client bank record';
ALTER TABLE client_banks MODIFY COLUMN client_code VARCHAR(50) COMMENT 'Client code associated with this bank account';
ALTER TABLE client_banks MODIFY COLUMN bank_name VARCHAR(255) COMMENT 'Name of the bank';
ALTER TABLE client_banks MODIFY COLUMN bank_branch VARCHAR(255) COMMENT 'Branch name of the bank';
ALTER TABLE client_banks MODIFY COLUMN bank_account_type VARCHAR(50) COMMENT 'Type of bank account (savings/current/etc)';
ALTER TABLE client_banks MODIFY COLUMN bank_account_number VARCHAR(50) COMMENT 'Bank account number';
ALTER TABLE client_banks MODIFY COLUMN bank_ifsc_code VARCHAR(20) COMMENT 'IFSC code of the bank branch';
ALTER TABLE client_banks MODIFY COLUMN bank_micr_code VARCHAR(20) COMMENT 'MICR code of the bank branch';
ALTER TABLE client_banks MODIFY COLUMN bank_code VARCHAR(50) COMMENT 'Internal bank code';
ALTER TABLE client_banks MODIFY COLUMN bank_address TEXT COMMENT 'Address of the bank branch';
ALTER TABLE client_banks MODIFY COLUMN default_bank VARCHAR(10) COMMENT 'Flag indicating if this is the default bank for the client';
ALTER TABLE client_banks MODIFY COLUMN created_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was created';
ALTER TABLE client_banks MODIFY COLUMN updated_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was last updated';

