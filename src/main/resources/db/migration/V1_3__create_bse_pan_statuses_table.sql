-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_3__create_bse_pan_statuses_table.sql

CREATE TABLE bse_pan_statuses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    client_code VARCHAR(10),
    client_name VA<PERSON>HA<PERSON>(255),
    a<PERSON><PERSON><PERSON>_seed_flag VARCHAR(10),
    created_on DATETIME,
    name_on_pan_card VARCHAR(255),
    pan_name VA<PERSON>HA<PERSON>(255),
    pan_number VARCHAR(10),
    pan_validation_on VARCHAR(50),
    pan_status VARCHAR(50),
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_bse_pan_statuses_client_code ON bse_pan_statuses(client_code);
CREATE INDEX idx_bse_pan_statuses_pan_number ON bse_pan_statuses(pan_number);
CREATE INDEX idx_bse_pan_statuses_pan_status ON bse_pan_statuses(pan_status);
CREATE INDEX idx_bse_pan_statuses_created_on ON bse_pan_statuses(created_on);
CREATE INDEX idx_bse_pan_statuses_created_at ON bse_pan_statuses(created_at);

-- Add comments for documentation
ALTER TABLE bse_pan_statuses COMMENT = 'Table storing BSE PAN status information for clients';
ALTER TABLE bse_pan_statuses MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT 'Primary key - unique identifier for each BSE PAN status record';
ALTER TABLE bse_pan_statuses MODIFY COLUMN client_code VARCHAR(50) COMMENT 'Unique code identifying the client';
ALTER TABLE bse_pan_statuses MODIFY COLUMN client_name VARCHAR(255) COMMENT 'Name of the client';
ALTER TABLE bse_pan_statuses MODIFY COLUMN aadhaar_seed_flag VARCHAR(10) COMMENT 'Flag indicating Aadhaar seeding status';
ALTER TABLE bse_pan_statuses MODIFY COLUMN created_on DATETIME COMMENT 'Date and time when the PAN status was created';
ALTER TABLE bse_pan_statuses MODIFY COLUMN name_on_pan_card VARCHAR(255) COMMENT 'Name as it appears on the PAN card';
ALTER TABLE bse_pan_statuses MODIFY COLUMN pan_name VARCHAR(255) COMMENT 'PAN holder name';
ALTER TABLE bse_pan_statuses MODIFY COLUMN pan_number VARCHAR(20) COMMENT 'PAN number';
ALTER TABLE bse_pan_statuses MODIFY COLUMN pan_validation_on VARCHAR(50) COMMENT 'Date/time when PAN validation was performed';
ALTER TABLE bse_pan_statuses MODIFY COLUMN pan_status VARCHAR(50) COMMENT 'Current status of the PAN validation';
ALTER TABLE bse_pan_statuses MODIFY COLUMN created_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was created';
ALTER TABLE bse_pan_statuses MODIFY COLUMN updated_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was last updated';