-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_2__create_banks_table.sql

CREATE TABLE banks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Create index for better performance on timestamp queries
CREATE INDEX idx_banks_created_at ON banks(created_at);

-- Add comments for documentation
ALTER TABLE banks COMMENT = 'Table storing bank information';
ALTER TABLE banks MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT 'Primary key - unique identifier for each bank';
ALTER TABLE banks MODIFY COLUMN created_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was created';
ALTER TABLE banks MODIFY COLUMN updated_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was last updated';