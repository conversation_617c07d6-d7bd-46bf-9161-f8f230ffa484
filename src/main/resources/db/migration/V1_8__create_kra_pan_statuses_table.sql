-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_8__create_kra_pan_statuses_table.sql

CREATE TABLE kra_pan_statuses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    app_pan_no VARCHAR(10),
    app_cor_add_proof VARCHAR(100),
    app_entry_dt DATETIME,
    app_hold_deactive_rmks TEXT,
    app_ipv_flag VARCHAR(10),
    app_kyc_mode VARCHAR(50),
    app_mod_dt DATETIME,
    app_name VARCHAR(255),
    app_per_add_proof VARCHAR(100),
    app_status VARCHAR(50),
    app_status_delta VARCHAR(50),
    app_status_message TEXT,
    app_status_dt DATETIME,
    app_ubo_flag VARCHAR(10),
    app_updt_rmks TEXT,
    app_updt_status VARCHAR(50),
    kra_code VARCHAR(50),
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Add foreign key constraint
ALTER TABLE kra_pan_statuses ADD CONSTRAINT fk_kra_pan_statuses_app_pan_no
FOREIGN KEY (app_pan_no) REFERENCES clients(pan_number);

-- Create indexes for better performance
CREATE INDEX idx_kra_pan_statuses_app_pan_no ON kra_pan_statuses(app_pan_no);
CREATE INDEX idx_kra_pan_statuses_kra_code ON kra_pan_statuses(kra_code);
CREATE INDEX idx_kra_pan_statuses_app_status ON kra_pan_statuses(app_status);
CREATE INDEX idx_kra_pan_statuses_app_name ON kra_pan_statuses(app_name);
CREATE INDEX idx_kra_pan_statuses_app_entry_dt ON kra_pan_statuses(app_entry_dt);
CREATE INDEX idx_kra_pan_statuses_app_mod_dt ON kra_pan_statuses(app_mod_dt);
CREATE INDEX idx_kra_pan_statuses_app_status_dt ON kra_pan_statuses(app_status_dt);
CREATE INDEX idx_kra_pan_statuses_app_ipv_flag ON kra_pan_statuses(app_ipv_flag);
CREATE INDEX idx_kra_pan_statuses_app_kyc_mode ON kra_pan_statuses(app_kyc_mode);
CREATE INDEX idx_kra_pan_statuses_created_at ON kra_pan_statuses(created_at);

-- Add comments for documentation
ALTER TABLE kra_pan_statuses COMMENT = 'Table storing KRA (KYC Registration Agency) PAN status information';
ALTER TABLE kra_pan_statuses MODIFY COLUMN id BIGINT AUTO_INCREMENT COMMENT 'Primary key - unique identifier for each KRA PAN status record';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_pan_no VARCHAR(20) COMMENT 'Application PAN number';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_cor_add_proof VARCHAR(100) COMMENT 'Application correspondence address proof type';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_entry_dt DATETIME COMMENT 'Application entry date';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_hold_deactive_rmks TEXT COMMENT 'Application hold/deactivation remarks';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_ipv_flag VARCHAR(10) COMMENT 'Application In-Person Verification flag';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_kyc_mode VARCHAR(50) COMMENT 'Application KYC mode used';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_mod_dt DATETIME COMMENT 'Application modification date';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_name VARCHAR(255) COMMENT 'Application holder name';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_per_add_proof VARCHAR(100) COMMENT 'Application permanent address proof type';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_status VARCHAR(50) COMMENT 'Current application status';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_status_delta VARCHAR(50) COMMENT 'Application status change delta';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_status_message TEXT COMMENT 'Application status message/description';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_status_dt DATETIME COMMENT 'Application status date';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_ubo_flag VARCHAR(10) COMMENT 'Application Ultimate Beneficial Owner flag';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_updt_rmks TEXT COMMENT 'Application update remarks';
ALTER TABLE kra_pan_statuses MODIFY COLUMN app_updt_status VARCHAR(50) COMMENT 'Application update status';
ALTER TABLE kra_pan_statuses MODIFY COLUMN kra_code VARCHAR(50) COMMENT 'KRA (KYC Registration Agency) code';
ALTER TABLE kra_pan_statuses MODIFY COLUMN created_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was created';
ALTER TABLE kra_pan_statuses MODIFY COLUMN updated_at TIMESTAMP(6) NOT NULL COMMENT 'Timestamp when the record was last updated';