-- Flyway Migration Script
-- File: src/main/resources/db/migration/V1_8__create_kra_pan_statuses_table.sql

CREATE TABLE kra_pan_statuses (
    id BIGSERIAL PRIMARY KEY,
    identifier VARCHAR(100) NOT NULL UNIQUE,
    app_pan_no VARCHAR(10),
    app_cor_add_proof VARCHAR(100),
    app_entry_dt TIMESTAMP,
    app_hold_deactive_rmks TEXT,
    app_ipv_flag VARCHAR(10),
    app_kyc_mode VARCHAR(50),
    app_mod_dt TIMESTAMP,
    app_name VARCHAR(255),
    app_per_add_proof VARCHAR(100),
    app_status VARCHAR(50),
    app_status_delta VARCHAR(50),
    app_status_message TEXT,
    app_status_dt TIMESTAMP,
    app_ubo_flag VARCHAR(10),
    app_updt_rmks TEXT,
    app_updt_status VARCHAR(50),
    kra_code VARCHAR(50),
    created_at TIMESTAMP(6) NOT NULL,
    updated_at TIMESTAMP(6) NOT NULL
);

-- Add foreign key constraint
ALTER TABLE kra_pan_statuses ADD CONSTRAINT fk_kra_pan_statuses_app_pan_no
FOREIGN KEY (app_pan_no) REFERENCES clients(pan_number);

-- Create indexes for better performance
CREATE INDEX idx_kra_pan_statuses_identifier ON kra_pan_statuses(identifier);
CREATE INDEX idx_kra_pan_statuses_app_pan_no ON kra_pan_statuses(app_pan_no);
CREATE INDEX idx_kra_pan_statuses_kra_code ON kra_pan_statuses(kra_code);
CREATE INDEX idx_kra_pan_statuses_app_status ON kra_pan_statuses(app_status);
CREATE INDEX idx_kra_pan_statuses_app_name ON kra_pan_statuses(app_name);
CREATE INDEX idx_kra_pan_statuses_app_entry_dt ON kra_pan_statuses(app_entry_dt);
CREATE INDEX idx_kra_pan_statuses_app_mod_dt ON kra_pan_statuses(app_mod_dt);
CREATE INDEX idx_kra_pan_statuses_app_status_dt ON kra_pan_statuses(app_status_dt);
CREATE INDEX idx_kra_pan_statuses_app_ipv_flag ON kra_pan_statuses(app_ipv_flag);
CREATE INDEX idx_kra_pan_statuses_app_kyc_mode ON kra_pan_statuses(app_kyc_mode);
CREATE INDEX idx_kra_pan_statuses_created_at ON kra_pan_statuses(created_at);

-- Add comments for documentation
COMMENT ON TABLE kra_pan_statuses IS 'Table storing KRA (KYC Registration Agency) PAN status information';
COMMENT ON COLUMN kra_pan_statuses.id IS 'Primary key - unique identifier for each KRA PAN status record';
COMMENT ON COLUMN kra_pan_statuses.app_pan_no IS 'Application PAN number';
COMMENT ON COLUMN kra_pan_statuses.app_cor_add_proof IS 'Application correspondence address proof type';
COMMENT ON COLUMN kra_pan_statuses.app_entry_dt IS 'Application entry date';
COMMENT ON COLUMN kra_pan_statuses.app_hold_deactive_rmks IS 'Application hold/deactivation remarks';
COMMENT ON COLUMN kra_pan_statuses.app_ipv_flag IS 'Application In-Person Verification flag';
COMMENT ON COLUMN kra_pan_statuses.app_kyc_mode IS 'Application KYC mode used';
COMMENT ON COLUMN kra_pan_statuses.app_mod_dt IS 'Application modification date';
COMMENT ON COLUMN kra_pan_statuses.app_name IS 'Application holder name';
COMMENT ON COLUMN kra_pan_statuses.app_per_add_proof IS 'Application permanent address proof type';
COMMENT ON COLUMN kra_pan_statuses.app_status IS 'Current application status';
COMMENT ON COLUMN kra_pan_statuses.app_status_delta IS 'Application status change delta';
COMMENT ON COLUMN kra_pan_statuses.app_status_message IS 'Application status message/description';
COMMENT ON COLUMN kra_pan_statuses.app_status_dt IS 'Application status date';
COMMENT ON COLUMN kra_pan_statuses.app_ubo_flag IS 'Application Ultimate Beneficial Owner flag';
COMMENT ON COLUMN kra_pan_statuses.app_updt_rmks IS 'Application update remarks';
COMMENT ON COLUMN kra_pan_statuses.app_updt_status IS 'Application update status';
COMMENT ON COLUMN kra_pan_statuses.kra_code IS 'KRA (KYC Registration Agency) code';
COMMENT ON COLUMN kra_pan_statuses.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN kra_pan_statuses.updated_at IS 'Timestamp when the record was last updated';